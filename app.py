#!/usr/bin/env python3
"""
No Code AI/ML Data Platform
===========================

A comprehensive no-code platform for AI/ML, Deep Learning, Reinforcement Learning,
GenAI, Data Science, Engineering, and Analysis using AutoGluon and NVIDIA RAPIDS.

Author: AI Platform Team
Version: 1.0.0
License: MIT
"""

import os
import sys
import warnings
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import streamlit as st
from PIL import Image
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

# Import comprehensive modules
try:
    from modules import (
        computer_vision, nlp, time_series, reinforcement_learning,
        generative_ai, graph_neural_networks, quantum_ml, federated_learning,
        mlops, advanced_analytics, edge_ai, audio_processing
    )
    COMPREHENSIVE_MODULES_AVAILABLE = True
except ImportError as e:
    st.warning(f"Some advanced modules not available: {str(e)}")
    COMPREHENSIVE_MODULES_AVAILABLE = False

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configuration
st.set_page_config(
    page_title="No Code AI/ML Platform",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)


class TaskType(Enum):
    """Enumeration of supported ML task types."""
    TABULAR_CLASSIFICATION = "tabular_classification"
    TABULAR_REGRESSION = "tabular_regression"
    IMAGE_CLASSIFICATION = "image_classification"
    TEXT_CLASSIFICATION = "text_classification"
    OBJECT_DETECTION = "object_detection"
    TIME_SERIES = "time_series"
    REINFORCEMENT_LEARNING = "reinforcement_learning"


class ModelFramework(Enum):
    """Enumeration of supported ML frameworks."""
    AUTOGLUON = "autogluon"
    RAPIDS = "rapids"
    SKLEARN = "sklearn"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"


@dataclass
class ModelConfig:
    """Configuration class for ML models."""
    task_type: TaskType
    framework: ModelFramework
    target_column: Optional[str] = None
    time_limit: int = 600
    quality_preset: str = "medium_quality"
    eval_metric: Optional[str] = None


class DataProcessor(ABC):
    """Abstract base class for data processing operations."""

    @abstractmethod
    def load_data(self, file_path: str) -> pd.DataFrame:
        """Load data from file."""
        pass

    @abstractmethod
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess the data."""
        pass

    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate data quality."""
        pass


class RAPIDSDataProcessor(DataProcessor):
    """NVIDIA RAPIDS-accelerated data processor."""

    def __init__(self):
        """Initialize RAPIDS data processor."""
        try:
            import cudf
            import cuml
            self.cudf = cudf
            self.cuml = cuml
            self.rapids_available = True
        except ImportError:
            st.warning("RAPIDS not available. Falling back to pandas/sklearn.")
            self.rapids_available = False

    def load_data(self, file_path: str) -> pd.DataFrame:
        """Load data using RAPIDS cuDF if available."""
        try:
            if self.rapids_available and file_path.endswith('.csv'):
                df = self.cudf.read_csv(file_path)
                return df.to_pandas()  # Convert to pandas for compatibility
            else:
                return pd.read_csv(file_path)
        except Exception as e:
            st.error(f"Error loading data: {str(e)}")
            return pd.DataFrame()

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data with RAPIDS acceleration."""
        if data.empty:
            return data

        # Basic preprocessing
        processed_data = data.copy()

        # Handle missing values
        numeric_columns = processed_data.select_dtypes(include=[np.number]).columns
        processed_data[numeric_columns] = processed_data[numeric_columns].fillna(
            processed_data[numeric_columns].mean()
        )

        categorical_columns = processed_data.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            processed_data[col] = processed_data[col].fillna(processed_data[col].mode()[0] if not processed_data[col].mode().empty else 'Unknown')

        return processed_data

    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate data quality."""
        if data.empty:
            return False

        # Check for minimum number of rows
        if len(data) < 10:
            st.warning("Dataset too small. Need at least 10 rows.")
            return False

        return True


class AutoGluonModelTrainer:
    """AutoGluon model trainer for various ML tasks."""

    def __init__(self, config: ModelConfig):
        """Initialize AutoGluon trainer."""
        self.config = config
        self.predictor = None

        try:
            from autogluon.tabular import TabularPredictor
            from autogluon.vision import ImagePredictor
            from autogluon.text import TextPredictor
            from autogluon.timeseries import TimeSeriesPredictor

            self.TabularPredictor = TabularPredictor
            self.ImagePredictor = ImagePredictor
            self.TextPredictor = TextPredictor
            self.TimeSeriesPredictor = TimeSeriesPredictor
            self.autogluon_available = True
        except ImportError:
            st.error("AutoGluon not available. Please install AutoGluon.")
            self.autogluon_available = False

    def train_tabular_model(self, train_data: pd.DataFrame) -> Optional[Any]:
        """Train tabular model using AutoGluon."""
        if not self.autogluon_available:
            return None

        try:
            self.predictor = self.TabularPredictor(
                label=self.config.target_column,
                problem_type='binary' if self.config.task_type == TaskType.TABULAR_CLASSIFICATION else 'regression',
                eval_metric=self.config.eval_metric
            )

            self.predictor.fit(
                train_data=train_data,
                time_limit=self.config.time_limit,
                presets=self.config.quality_preset
            )

            return self.predictor
        except Exception as e:
            st.error(f"Error training model: {str(e)}")
            return None

    def predict(self, data: pd.DataFrame) -> Optional[pd.Series]:
        """Make predictions using trained model."""
        if self.predictor is None:
            st.error("No trained model available.")
            return None

        try:
            return self.predictor.predict(data)
        except Exception as e:
            st.error(f"Error making predictions: {str(e)}")
            return None

    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance from trained model."""
        if self.predictor is None:
            return None

        try:
            return self.predictor.feature_importance(train_data)
        except Exception as e:
            st.warning("Feature importance not available.")
            return None


class DataVisualizer:
    """Data visualization utilities."""

    @staticmethod
    def create_correlation_heatmap(data: pd.DataFrame) -> go.Figure:
        """Create correlation heatmap."""
        numeric_data = data.select_dtypes(include=[np.number])
        if numeric_data.empty:
            return go.Figure()

        corr_matrix = numeric_data.corr()

        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.columns,
            colorscale='RdBu',
            zmid=0
        ))

        fig.update_layout(
            title="Feature Correlation Heatmap",
            xaxis_title="Features",
            yaxis_title="Features"
        )

        return fig

    @staticmethod
    def create_distribution_plot(data: pd.DataFrame, column: str) -> go.Figure:
        """Create distribution plot for a column."""
        if column not in data.columns:
            return go.Figure()

        if data[column].dtype in ['int64', 'float64']:
            fig = px.histogram(data, x=column, title=f"Distribution of {column}")
        else:
            value_counts = data[column].value_counts()
            fig = px.bar(x=value_counts.index, y=value_counts.values,
                        title=f"Distribution of {column}")
            fig.update_xaxis(title=column)
            fig.update_yaxis(title="Count")

        return fig

    @staticmethod
    def create_feature_importance_plot(importance_df: pd.DataFrame) -> go.Figure:
        """Create feature importance plot."""
        if importance_df is None or importance_df.empty:
            return go.Figure()

        fig = px.bar(
            importance_df.head(20),
            x='importance',
            y='feature',
            orientation='h',
            title="Top 20 Feature Importance"
        )

        fig.update_layout(yaxis={'categoryorder': 'total ascending'})
        return fig


class NoCodeAIPlatform:
    """Main application class for the No Code AI/ML Platform."""

    def __init__(self):
        """Initialize the platform."""
        self.data_processor = RAPIDSDataProcessor()
        self.visualizer = DataVisualizer()
        self.current_data: Optional[pd.DataFrame] = None
        self.trained_model: Optional[Any] = None
        self.model_config: Optional[ModelConfig] = None

    def render_sidebar(self) -> None:
        """Render the sidebar navigation."""
        st.sidebar.title("🤖 No Code AI/ML Platform")
        st.sidebar.markdown("---")

        # Navigation
        page = st.sidebar.selectbox(
            "Navigate to:",
            [
                "🏠 Home",
                "📊 Data Upload & Analysis",
                "🔧 Data Preprocessing",
                "🤖 Model Training",
                "📈 Model Evaluation",
                "🔮 Predictions",
                "�️ Computer Vision",
                "📝 Natural Language Processing",
                "📈 Time Series Analysis",
                "🎯 Reinforcement Learning",
                "🎨 Generative AI",
                "🕸️ Graph Neural Networks",
                "⚛️ Quantum Machine Learning",
                "🌐 Federated Learning",
                "🔧 MLOps",
                "📊 Advanced Analytics",
                "📱 Edge AI",
                "🎵 Audio Processing"
            ]
        )

        st.sidebar.markdown("---")
        st.sidebar.markdown("### Platform Features")
        st.sidebar.markdown("""
        - **AutoGluon Integration** 🚀
        - **NVIDIA RAPIDS** ⚡
        - **No Code Interface** 🎯
        - **Multiple ML Tasks** 🔧
        - **Real-time Visualization** 📊
        - **Model Deployment** 🌐
        """)

        return page

    def render_home_page(self) -> None:
        """Render the home page."""
        st.title("🤖 No Code AI/ML Data Platform")
        st.markdown("### Welcome to the Future of Machine Learning!")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            #### 🚀 AutoGluon Powered
            - Automated ML Pipeline
            - State-of-the-art Models
            - Minimal Configuration
            - Best-in-class Performance
            """)

        with col2:
            st.markdown("""
            #### ⚡ RAPIDS Accelerated
            - GPU Acceleration
            - Lightning Fast Processing
            - Scalable Computing
            - Memory Efficient
            """)

        with col3:
            st.markdown("""
            #### 🎯 No Code Required
            - Drag & Drop Interface
            - Visual Model Building
            - Automated Preprocessing
            - One-Click Deployment
            """)

        st.markdown("---")

        # Platform capabilities
        st.markdown("### 🔧 Platform Capabilities")

        capabilities = {
            "Tabular ML": ["Classification", "Regression", "Feature Selection"],
            "Computer Vision": ["Image Classification", "Object Detection", "Image Segmentation"],
            "NLP": ["Text Classification", "Sentiment Analysis", "Named Entity Recognition"],
            "Time Series": ["Forecasting", "Anomaly Detection", "Trend Analysis"],
            "GenAI": ["Text Generation", "Code Generation", "Creative Writing"],
            "Reinforcement Learning": ["Game AI", "Optimization", "Decision Making"]
        }

        cols = st.columns(3)
        for i, (category, tasks) in enumerate(capabilities.items()):
            with cols[i % 3]:
                st.markdown(f"**{category}**")
                for task in tasks:
                    st.markdown(f"• {task}")

    def render_data_upload_page(self) -> None:
        """Render data upload and analysis page."""
        st.title("📊 Data Upload & Analysis")

        # File upload
        uploaded_file = st.file_uploader(
            "Upload your dataset",
            type=['csv', 'xlsx', 'json', 'parquet'],
            help="Supported formats: CSV, Excel, JSON, Parquet"
        )

        if uploaded_file is not None:
            try:
                # Load data based on file type
                if uploaded_file.name.endswith('.csv'):
                    self.current_data = pd.read_csv(uploaded_file)
                elif uploaded_file.name.endswith('.xlsx'):
                    self.current_data = pd.read_excel(uploaded_file)
                elif uploaded_file.name.endswith('.json'):
                    self.current_data = pd.read_json(uploaded_file)
                elif uploaded_file.name.endswith('.parquet'):
                    self.current_data = pd.read_parquet(uploaded_file)

                if self.current_data is not None:
                    st.success(f"✅ Data loaded successfully! Shape: {self.current_data.shape}")

                    # Data overview
                    col1, col2 = st.columns(2)

                    with col1:
                        st.markdown("### Dataset Overview")
                        st.dataframe(self.current_data.head())

                    with col2:
                        st.markdown("### Data Statistics")
                        st.dataframe(self.current_data.describe())

                    # Data quality checks
                    st.markdown("### Data Quality Analysis")

                    quality_col1, quality_col2, quality_col3 = st.columns(3)

                    with quality_col1:
                        missing_data = self.current_data.isnull().sum()
                        missing_pct = (missing_data / len(self.current_data)) * 100
                        st.markdown("**Missing Values**")
                        for col, pct in missing_pct.items():
                            if pct > 0:
                                st.write(f"{col}: {pct:.1f}%")

                    with quality_col2:
                        st.markdown("**Data Types**")
                        dtype_counts = self.current_data.dtypes.value_counts()
                        for dtype, count in dtype_counts.items():
                            st.write(f"{dtype}: {count} columns")

                    with quality_col3:
                        st.markdown("**Dataset Info**")
                        st.write(f"Rows: {len(self.current_data):,}")
                        st.write(f"Columns: {len(self.current_data.columns)}")
                        st.write(f"Memory: {self.current_data.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

                    # Visualizations
                    if st.checkbox("Show Correlation Heatmap"):
                        fig = self.visualizer.create_correlation_heatmap(self.current_data)
                        st.plotly_chart(fig, use_container_width=True)

                    # Column distribution
                    if st.checkbox("Show Column Distributions"):
                        selected_column = st.selectbox("Select column:", self.current_data.columns)
                        fig = self.visualizer.create_distribution_plot(self.current_data, selected_column)
                        st.plotly_chart(fig, use_container_width=True)

            except Exception as e:
                st.error(f"Error loading data: {str(e)}")

    def render_preprocessing_page(self) -> None:
        """Render data preprocessing page."""
        st.title("🔧 Data Preprocessing")

        if self.current_data is None:
            st.warning("⚠️ Please upload data first!")
            return

        st.markdown("### Current Dataset")
        st.dataframe(self.current_data.head())

        # Preprocessing options
        st.markdown("### Preprocessing Options")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### Missing Value Handling")
            missing_strategy = st.selectbox(
                "Strategy for numeric columns:",
                ["mean", "median", "mode", "drop"]
            )

            categorical_strategy = st.selectbox(
                "Strategy for categorical columns:",
                ["mode", "constant", "drop"]
            )

        with col2:
            st.markdown("#### Feature Engineering")
            scale_features = st.checkbox("Scale numeric features")
            encode_categorical = st.checkbox("Encode categorical features")
            remove_outliers = st.checkbox("Remove outliers")

        if st.button("Apply Preprocessing"):
            with st.spinner("Processing data..."):
                processed_data = self.data_processor.preprocess_data(self.current_data)

                if scale_features:
                    from sklearn.preprocessing import StandardScaler
                    numeric_cols = processed_data.select_dtypes(include=[np.number]).columns
                    if len(numeric_cols) > 0:
                        scaler = StandardScaler()
                        processed_data[numeric_cols] = scaler.fit_transform(processed_data[numeric_cols])

                if encode_categorical:
                    from sklearn.preprocessing import LabelEncoder
                    categorical_cols = processed_data.select_dtypes(include=['object']).columns
                    for col in categorical_cols:
                        le = LabelEncoder()
                        processed_data[col] = le.fit_transform(processed_data[col].astype(str))

                self.current_data = processed_data
                st.success("✅ Preprocessing completed!")
                st.dataframe(self.current_data.head())

    def render_model_training_page(self) -> None:
        """Render model training page."""
        st.title("🤖 Model Training")

        if self.current_data is None:
            st.warning("⚠️ Please upload and preprocess data first!")
            return

        # Model configuration
        st.markdown("### Model Configuration")

        col1, col2 = st.columns(2)

        with col1:
            task_type = st.selectbox(
                "Select ML Task:",
                [e.value for e in TaskType if e in [TaskType.TABULAR_CLASSIFICATION, TaskType.TABULAR_REGRESSION]]
            )

            target_column = st.selectbox(
                "Select target column:",
                self.current_data.columns.tolist()
            )

        with col2:
            time_limit = st.slider("Training time limit (seconds):", 60, 3600, 600)
            quality_preset = st.selectbox(
                "Quality preset:",
                ["medium_quality", "high_quality", "best_quality"]
            )

        # Advanced settings
        with st.expander("Advanced Settings"):
            eval_metric = st.selectbox(
                "Evaluation metric:",
                ["auto", "accuracy", "roc_auc", "f1", "rmse", "mae"]
            )

        if st.button("🚀 Train Model"):
            # Create model configuration
            self.model_config = ModelConfig(
                task_type=TaskType(task_type),
                framework=ModelFramework.AUTOGLUON,
                target_column=target_column,
                time_limit=time_limit,
                quality_preset=quality_preset,
                eval_metric=eval_metric if eval_metric != "auto" else None
            )

            # Initialize trainer
            trainer = AutoGluonModelTrainer(self.model_config)

            with st.spinner("Training model... This may take a while."):
                self.trained_model = trainer.train_tabular_model(self.current_data)

            if self.trained_model is not None:
                st.success("✅ Model trained successfully!")

                # Model summary
                try:
                    leaderboard = self.trained_model.leaderboard()
                    st.markdown("### Model Leaderboard")
                    st.dataframe(leaderboard)
                except:
                    st.info("Model leaderboard not available.")
            else:
                st.error("❌ Model training failed!")

    def render_model_evaluation_page(self) -> None:
        """Render model evaluation page."""
        st.title("📈 Model Evaluation")

        if self.trained_model is None:
            st.warning("⚠️ Please train a model first!")
            return

        st.markdown("### Model Performance")

        try:
            # Get model summary
            leaderboard = self.trained_model.leaderboard()
            best_model = leaderboard.iloc[0]

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Best Model", best_model['model'])

            with col2:
                score_col = 'score_val' if 'score_val' in leaderboard.columns else leaderboard.columns[1]
                st.metric("Validation Score", f"{best_model[score_col]:.4f}")

            with col3:
                st.metric("Training Time", f"{best_model.get('fit_time', 'N/A'):.1f}s" if isinstance(best_model.get('fit_time'), (int, float)) else "N/A")

            # Feature importance
            try:
                importance = self.trained_model.feature_importance(self.current_data)
                if importance is not None and not importance.empty:
                    st.markdown("### Feature Importance")
                    fig = self.visualizer.create_feature_importance_plot(importance)
                    st.plotly_chart(fig, use_container_width=True)
            except:
                st.info("Feature importance not available for this model.")

        except Exception as e:
            st.error(f"Error displaying model evaluation: {str(e)}")

    def render_predictions_page(self) -> None:
        """Render predictions page."""
        st.title("🔮 Predictions")

        if self.trained_model is None:
            st.warning("⚠️ Please train a model first!")
            return

        st.markdown("### Make Predictions")

        # Option to upload new data or use existing
        prediction_option = st.radio(
            "Choose prediction data:",
            ["Use training data sample", "Upload new data"]
        )

        if prediction_option == "Upload new data":
            pred_file = st.file_uploader("Upload data for predictions", type=['csv'])
            if pred_file is not None:
                pred_data = pd.read_csv(pred_file)
                st.dataframe(pred_data.head())
            else:
                pred_data = None
        else:
            pred_data = self.current_data.drop(columns=[self.model_config.target_column]).head(10)
            st.markdown("### Sample Data (first 10 rows)")
            st.dataframe(pred_data)

        if pred_data is not None and st.button("Generate Predictions"):
            with st.spinner("Generating predictions..."):
                try:
                    predictions = self.trained_model.predict(pred_data)

                    # Display predictions
                    results_df = pred_data.copy()
                    results_df['Prediction'] = predictions

                    st.markdown("### Prediction Results")
                    st.dataframe(results_df)

                    # Download predictions
                    csv = results_df.to_csv(index=False)
                    st.download_button(
                        label="📥 Download Predictions",
                        data=csv,
                        file_name="predictions.csv",
                        mime="text/csv"
                    )

                except Exception as e:
                    st.error(f"Error generating predictions: {str(e)}")

    def render_genai_page(self) -> None:
        """Render GenAI tools page."""
        st.title("📱 GenAI Tools")
        st.markdown("### Generative AI Capabilities")

        genai_option = st.selectbox(
            "Select GenAI Tool:",
            [
                "Text Generation",
                "Code Generation",
                "Data Synthesis",
                "Model Explanation"
            ]
        )

        if genai_option == "Text Generation":
            st.markdown("#### Text Generation")
            prompt = st.text_area("Enter your prompt:", height=100)
            if st.button("Generate Text"):
                st.info("Text generation would be implemented with your preferred LLM API (OpenAI, Anthropic, etc.)")

        elif genai_option == "Code Generation":
            st.markdown("#### Code Generation")
            task_description = st.text_area("Describe the code you need:", height=100)
            language = st.selectbox("Programming Language:", ["Python", "R", "SQL", "JavaScript"])
            if st.button("Generate Code"):
                st.info("Code generation would be implemented with your preferred LLM API.")

        elif genai_option == "Data Synthesis":
            st.markdown("#### Synthetic Data Generation")
            if self.current_data is not None:
                num_samples = st.slider("Number of synthetic samples:", 10, 1000, 100)
                if st.button("Generate Synthetic Data"):
                    st.info("Synthetic data generation using statistical methods or GANs would be implemented here.")
            else:
                st.warning("Upload data first to generate synthetic samples.")

        elif genai_option == "Model Explanation":
            st.markdown("#### Model Explanation")
            if self.trained_model is not None:
                if st.button("Generate Model Explanation"):
                    st.info("AI-powered model explanation would be generated here using LLMs.")
            else:
                st.warning("Train a model first to generate explanations.")

    def render_rl_page(self) -> None:
        """Render Reinforcement Learning page."""
        st.title("🎯 Reinforcement Learning")
        st.markdown("### RL Environment Setup")

        rl_environment = st.selectbox(
            "Select RL Environment:",
            [
                "Custom Environment",
                "OpenAI Gym",
                "Trading Environment",
                "Game Environment"
            ]
        )

        if rl_environment == "Custom Environment":
            st.markdown("#### Define Custom Environment")

            col1, col2 = st.columns(2)
            with col1:
                state_space = st.number_input("State Space Dimension:", min_value=1, value=4)
                action_space = st.number_input("Action Space Dimension:", min_value=1, value=2)

            with col2:
                reward_function = st.text_area("Reward Function (Python code):",
                                             value="def reward_function(state, action, next_state):\n    return 1.0")

            algorithm = st.selectbox("RL Algorithm:", ["DQN", "PPO", "A3C", "SAC"])

            if st.button("Initialize RL Training"):
                st.info("RL training would be implemented using frameworks like Ray RLlib, Stable-Baselines3, etc.")

        else:
            st.info(f"Integration with {rl_environment} would be implemented here.")

    def run(self) -> None:
        """Run the main application."""
        # Render sidebar and get current page
        current_page = self.render_sidebar()

        # Route to appropriate page
        if current_page == "🏠 Home":
            self.render_home_page()
        elif current_page == "📊 Data Upload & Analysis":
            self.render_data_upload_page()
        elif current_page == "🔧 Data Preprocessing":
            self.render_preprocessing_page()
        elif current_page == "🤖 Model Training":
            self.render_model_training_page()
        elif current_page == "📈 Model Evaluation":
            self.render_model_evaluation_page()
        elif current_page == "🔮 Predictions":
            self.render_predictions_page()
        elif current_page == "📱 GenAI Tools":
            self.render_genai_page()
        elif current_page == "🎯 Reinforcement Learning":
            self.render_rl_page()


def main() -> None:
    """Main application entry point."""
    try:
        # Initialize and run the platform
        platform = NoCodeAIPlatform()
        platform.run()

    except Exception as e:
        st.error(f"Application error: {str(e)}")
        st.info("Please refresh the page and try again.")


if __name__ == "__main__":
    main()