adagio-0.2.6.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
adagio-0.2.6.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
adagio-0.2.6.dist-info/METADATA,sha256=3oP107y89WBauBnwMru7gHV82TafIn0vg1Ws5KlYm74,1806
adagio-0.2.6.dist-info/RECORD,,
adagio-0.2.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
adagio-0.2.6.dist-info/WHEEL,sha256=eOLhNAGa2EW3wWl_TU484h7q1UNgy0JXjjoqKoxAAQc,92
adagio-0.2.6.dist-info/top_level.txt,sha256=Hp7uRB6iD-OTx6EJQ7CvZIjXCf2UkH4wkx16bMArtOA,28
adagio/__init__.py,sha256=8qP8O1PJvTkqSx-k6zVS5wpK0jdSrWDDy6UcG0urk2E,54
adagio/exceptions.py,sha256=vnVFisnjgva3sRxlaIj6rBSLxQ-eMG-HmeniwA3Jyy4,736
adagio/instances.py,sha256=ZyQvHHk_XDxOyizJFqVLUsW8SzyuSmAtewB3BIJSaz0,26484
adagio/shells/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
adagio/shells/interfaceless.py,sha256=dfR0t4lggWwEKQ_8BHxnGK0nXdvrLkE3CbvH9Dny0LQ,4711
adagio/specs.py,sha256=5I783RvzuY_zUHMKOCUjJtOsDEqTQLaunoUJFUJm1z0,15140
adagio_version/__init__.py,sha256=Oz5HbwHMyE87nmwV80AZzpkJPf-wBg7eDuJr_BXZkhU,22
tests/shells/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/shells/test_interfaceless.py,sha256=mOyENcaEcD-Xnl8Voqs9W-PRAuG6O7kgXQa_N3Zp-qA,4423
