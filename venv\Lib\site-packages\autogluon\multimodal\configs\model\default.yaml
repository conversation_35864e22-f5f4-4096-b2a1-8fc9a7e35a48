model:
  names:
  categorical_mlp:
    hidden_size: 64
    activation: "leaky_relu"
    num_layers: 1
    dropout: 0.1
    normalization: "layer_norm"
    data_types:
      - "categorical"

  numerical_mlp:
    hidden_size: 128
    activation: "leaky_relu"
    num_layers: 1
    dropout: 0.1
    normalization: "layer_norm"
    token_dim: 8
    embedding_arch:
    data_types:
      - "numerical"
    merge: "concat"

  hf_text:
    checkpoint_name: "google/electra-base-discriminator"
    gradient_checkpointing: False
    pooling_mode: 'cls'  # The pooling mode, can be 'cls' or 'mean'
    data_types:
      - "text"
    tokenizer_name: "hf_auto"
    use_fast: True  # Use a fast Rust-based tokenizer if it is supported for a given model. If a fast tokenizer is not available for a given model, a normal Python-based tokenizer is returned instead.
    max_text_len: 512  # If None or <=0, then use the max length of pretrained models.
    insert_sep: True
    low_cpu_mem_usage: False
    text_segment_num: 2
    stochastic_chunk: False
    text_aug_detect_length: 10                # We perform text augmentation only if a text has more than text_detection_length words. It is used to differentiate text columns versus tabular columns that are treated as text.
    text_trivial_aug_maxscale: 0.1            # augmentation magnitude randomly drawn from [0, text_trivial_aug_maxscale]
    text_train_augment_types:        # specify augmentation space manually, will randomly select one from the following and identity
      # - "random_swap(0.05)"          # less than 0.1 based on eda paper
      # - "random_delete(0.05)"        # less than 0.1 based on eda paper
      # - "syn_replacement(0.05)"  # less than 0.1 based on eda paper
      # - "insert_punc(0.05)"

  ner_text:
    checkpoint_name: "bert-base-cased"
    max_text_len: 512
    gradient_checkpointing: False
    low_cpu_mem_usage: False
    data_types:
      - "text_ner"
    tokenizer_name: "hf_auto"
    insert_sep: False
    text_segment_num: 2
    stochastic_chunk: False
    special_tags: 
      - X # CLS, SEP, and non-first tokens of a word will be labelled as X
      - O # Outside of a named entity

  document_transformer:
    checkpoint_name: "microsoft/layoutlmv3-base" # document foundation models
    gradient_checkpointing: False
    pooling_mode: 'cls'  # The pooling mode, can be 'cls' or 'mean'
    data_types:
      - "document"
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "imagenet"
    image_size: 224
    tokenizer_name: "hf_auto"
    max_text_len: 512  # If None or <=0, then use the max length of pretrained models.
    insert_sep: True
    low_cpu_mem_usage: False
    text_segment_num: 2
    stochastic_chunk: False
    text_aug_detect_length: 10                # We perform text augmentation only if a text has more than text_detection_length words. It is used to differentiate text columns versus tabular columns that are treated as text.
    text_trivial_aug_maxscale: 0.0            # augmentation magnitude randomly drawn from [0, text_trivial_aug_maxscale]

  t_few:
    checkpoint_name: "t5-small" #"bigscience/T0_3B"
    gradient_checkpointing: False
    data_types:
      - "text"
    tokenizer_name: "hf_auto"
    length_norm: 1.0 # Normalizes length to adjust for length bias in target template
    unlikely_loss: 1.0 # Adds loss term that lowers probability of incorrect outputs
    mc_loss: 1.0 # Adds multiple choice cross entropy loss
    max_text_len: 512  # If None or <=0, then use the max length of pretrained models.
    text_segment_num: 2
    insert_sep: True
    low_cpu_mem_usage: False
    stochastic_chunk: False
    text_aug_detect_length: 10                # We perform text augmentation only if a text has more than text_detection_length words. It is used to differentiate text columns versus tabular columns that are treated as text.
    text_trivial_aug_maxscale: 0.0            # augmentation magnititude randomly drawn from [0, text_trivial_aug_maxscale]
    text_train_augment_types:

  timm_image:
    checkpoint_name: "swin_base_patch4_window7_224"
    mix_choice: "all_logits"
    data_types:
      - "image"
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
      - "trivial_augment"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "imagenet"
    image_size: null
    image_chan_num: 3
    use_learnable_image: False
    max_image_num_per_column: 1

  mmdet_image:
    checkpoint_name: "yolov3_mobilenetv2_8xb24-320-300e_coco"
    config_file: ""
    data_types:
      - "image"
    max_img_num_per_col: 1
    output_bbox_format: "xyxy"  # now support xyxy or xywh, for bbox format details see https://keras.io/api/keras_cv/bounding_box/formats/
    frozen_layers: null
    coco_root: null

  mmocr_text_detection:
    checkpoint_name: "TextSnake"
    data_types:
      - "image"
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
      - "trivial_augment"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "imagenet"
    image_size: 224
    max_img_num_per_col: 2

  mmocr_text_recognition:
    checkpoint_name: "ABINet"
    data_types:
      - "image"
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
      - "trivial_augment"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "imagenet"
    image_size: 224
    max_img_num_per_col: 2

  clip:
    checkpoint_name: "openai/clip-vit-base-patch32"
    data_types:
      - "image"
      - "text"
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
      - "trivial_augment"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "clip"
    image_size: 224
    image_chan_num: 3
    use_learnable_image: False
    max_image_num_per_column: 1
    tokenizer_name: "clip"
    max_text_len: 77  # The maximum possible length.
    insert_sep: False
    text_segment_num: 1
    stochastic_chunk: False
    text_aug_detect_length: 10                     # We perform text augmentation only if a text has more than text_detection_length words. It is used to differentiate text columns versus tabular columns that are treated as text.
    text_trivial_aug_maxscale: 0.0                      # scale randomly drawn from [0, text_trivial_aug_maxscale]
    text_train_augment_types:        # specify augmentation space manually, will randomly select one from the following and identity
      # - "random_swap(0.05)"          # less than 0.1 based on eda paper
      # - "random_delete(0.05)"        # less than 0.1 based on eda paper
      # - "syn_replacement(0.05)"  # less than 0.1 based on eda paper
      # - "insert_punc(0.05)"

  fusion_mlp:
    aux_loss_weight:
    adapt_in_features: "max"
    hidden_sizes:
      - 128
    activation: "leaky_relu"
    dropout: 0.1
    normalization: "layer_norm"
    data_types:

  fusion_ner:
    weight:
    adapt_in_features: "max"
    hidden_sizes:
      - 128
    activation: "leaky_relu"
    drop_rate: 0.1
    normalization: "layer_norm"
    data_types:

  fusion_transformer:
    aux_loss_weight:
    hidden_size: 192
    num_blocks: 3
    attention_num_heads: 8
    adapt_in_features: "max"
    attention_dropout: 0.2
    residual_dropout: 0.0
    ffn_dropout: 0.1
    ffn_hidden_size: 192
    normalization: "layer_norm"
    ffn_activation: "geglu"
    head_activation: "relu"
    data_types:
    additive_attention: False # Whether to use lightweight additive attention, can be True, False or "auto"
    share_qv_weights: False # Whether to share weight for query and value, can be True, False or "auto"

  ft_transformer:
    data_types:
      - "categorical"
      - "numerical"
    embedding_arch:
      - 'linear'
    token_dim: 192
    hidden_size: 192
    num_blocks: 3
    attention_num_heads: 8
    attention_dropout: 0.2
    residual_dropout: 0.0
    ffn_dropout: 0.1
    ffn_hidden_size: 192
    ffn_activation: "geglu"
    head_activation: "relu"
    normalization: "layer_norm"
    merge: "concat"
    requires_all_dtypes: False
    additive_attention: False # Whether to use lightweight additive attention, can be True, False or "auto"
    share_qv_weights: False # Whether to share weight for query and value, can be True, False or "auto"
    pooling_mode: "cls"
    checkpoint_name: null

  sam:
    checkpoint_name: "facebook/sam-vit-huge"
    image_norm: "imagenet"
    data_types:
      - "semantic_segmentation_img"
    train_transforms:
      - "random_horizontal_flip"
    val_transforms: []
    img_transforms:
      - "resize_to_square"
    gt_transforms:
      - "resize_gt_to_square"
    max_img_num_per_col: 1
    frozen_layers: ["mask_decoder.iou_prediction_head", "prompt_encoder"]
    num_mask_tokens: 1
    ignore_label: 255

  meta_transformer:
    data_types:
      - "image"
      - "text"
      - "categorical"
      - "numerical"
    checkpoint_path: null
    model_version: "base"
    requires_all_dtypes: False
    train_transforms:
      - "resize_shorter_side"
      - "center_crop"
      - "trivial_augment"
    val_transforms:
      - "resize_shorter_side"
      - "center_crop"
    image_norm: "imagenet"
    image_size: 224
    image_chan_num: 3
    use_learnable_image: False
    max_image_num_per_column: 1
    tokenizer_name: "hf_auto"
    max_text_len: 512  # If None or <=0, then use the max length of pretrained models.
    insert_sep: True
    text_segment_num: 2
    stochastic_chunk: False
    text_aug_detect_length: 10     # We perform text augmentation only if a text has more than text_detection_length words. It is used to differentiate text columns versus tabular columns that are treated as text.
    text_trivial_aug_maxscale: 0.1  # augmentation magnitude randomly drawn from [0, text_trivial_aug_maxscale]
    text_train_augment_types:
    merge: "concat"
