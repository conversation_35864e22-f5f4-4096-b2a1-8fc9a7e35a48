optim:
  optim_type: "adamw"
  lr: 1.0e-4
  weight_decay: 0.001
  lr_choice: "layerwise_decay"
  lr_decay: 0.9
  lr_schedule: "cosine_decay"
  max_epochs: 20
  max_steps: -1
  warmup_steps: 0.1
  end_lr: 0
  lr_mult: 1 # multiply lr for downstream heads
  patience: 10
  val_check_interval: 0.5 # Check validation score a fraction of epoch if float and every n steps if integer with n < total step in epoch.
  check_val_every_n_epoch: 1 # Check validation score every n epochs.
  skip_final_val: False # Flag to skip the last validation
  gradient_clip_val: 1
  gradient_clip_algorithm: "norm"
  track_grad_norm: -1 # Whether to check gradient norm. We can set it to 2 to check for gradient norm.
  log_every_n_steps: 10
  label_smoothing: 0
  top_k: 3
  top_k_average_method:
    "greedy_soup" # We support averaging method described in https://arxiv.org/pdf/2203.05482.pdf.
    # Currently support "uniform_soup", "greedy_soup", and "best".
  peft: null # Can be 'bit_fit' (only finetune bias), 'norm_fit' (finetune the normalization terms + bias terms), lora (LoRA Adaptations only), lora_bias (LoRA Adaptation + bit_fit), lora_norm (LoRA Adaptation + norm_fit), or null
  lora:
    module_filter: null # Specify which module (if any) to adapt(e.g. ".*EncDecAttention|.*DenseReluDense"). Default consider all modules in a model (i.e. empty filter).
    filter: # Specify which layer in a module to adapt. Default fine-tune only query and value attention weights, recommended in https://arxiv.org/abs/2106.09685
      - "query"
      - "value"
      - "^q$" # The filter can also be a regex. We will use re.match(filter, name) to determine if LoRA should be applied.
      - "^v$"
      - "^k$"
      - "^o$"
    r: 8
    alpha: 8
    conv_lora_expert_num: 8 # default setting for Conv-LoRA
  loss_func:
    "auto" # The replaced loss for regression. Can only support loss function in torch.nn.
    # example
    # "BCEWithLogitsLoss" or "nn.BCEWithLogitsloss"
  focal_loss:
    alpha: null
    gamma: 2.0
    reduction: "mean"
  mask2former_loss:
    loss_cross_entropy_weight: 10.0
    loss_mask_weight: 5.0
    loss_dice_weight: 5.0
  extra_trainable_params: []
  cross_modal_align: null
  cross_modal_align_weight: 0
  automatic_optimization: True
  lemda:
    turn_on: False
    arch_type: "mlp_vae"
    z_dim: 8
    num_layers: 6
    kld_weight: 0.1
    mse_weight: 0.1
    adv_weight: 1.0e-4
    consist_weight: 0.01
    consist_threshold: 0.5
    lr: 1.0e-4
    optim_type: "adamw"
    weight_decay: 1.0e-5
